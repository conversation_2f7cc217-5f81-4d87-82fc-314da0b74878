---
type: "always_apply"
---

# Development Guidelines
## 🚀 AUTOMATION STATUS
**ACTIVE SYSTEMS**: Prompt enhancement are **automated**
- **Prompt Enhancement**: Auto-applies 4-D methodology (see `prompt-enhancement.md`)
- **Sequential Thinking**: Mandatory for all tasks - break down requirements, analyze dependencies, plan implementation
- **WBS**: Analyze and follow tasks described in `docs/WBS.md`

---

## 🛠️ AVAILABLE MCP TOOLS
**CONFIGURED TOOLS**: Three MCP servers provide specialized capabilities for development

### Sequential Thinking
- **Server**: `@modelcontextprotocol/server-sequential-thinking`  
- **Capabilities**: Structured problem decomposition, step-by-step analysis, adaptive planning
- **Usage**: Complex task breakdown, dependency analysis, implementation planning
- **Integration**: Mandatory for all development tasks per automation requirements

### Web Research
- **Server**: `@modelcontextprotocol/server-brave-search`
- **Capabilities**: Real-time web search, technical documentation lookup, API research
- **Usage**: Pine <PERSON> references, Python library documentation, trading indicator research
- **Authentication**: Configured with Brave API key for unlimited searches

### Library Documentation
- **Server**: `@upstash/context7-mcp`
- **Capabilities**: Up-to-date library docs, code examples, API references
- **Usage**: pandas/numpy/ta-lib documentation, python-binance API guidance
- **Coverage**: Real-time access to latest library versions and examples

### Tool Integration Guidelines
- **Sequential First**: Always start complex tasks with sequential thinking
- **Research Before Code**: Use web search and Context7 for technical validation
- **File Operations**: Prefer editing existing files over creating new ones
- **Documentation**: Leverage Context7 for accurate library usage patterns

---

## 🎯 PROJECT CONTEXT: VuManChu Cipher B Python Conversion
### Overview
**Pine Script to Python conversion** (30,698 character indicator) for real-time trading system
- **Stakeholder**: Apoorv Mintri (AI Engineer & Solutions Architect)  
- **Timeline**: 6-phase approach (see `docs/WBS.md`)
- **Scope**: 7+ technical indicators, 50+ parameters, sub-second calculations

### Core Components
**WaveTrend Oscillator** (Primary) • **RSI+MFI Combo** • **Stochastic RSI** • **Schaff Trend Cycle** • **Sommi Pattern Recognition** • **Multi-timeframe Analysis**

### Technology Stack
- **Core**: pandas, numpy, ta-lib
- **Data**: python-binance (websocket + historical API)  
- **Visualization**: TradingView Lightweight Charts
- **Architecture**: 5-module system (Data Source → Data Manager → Indicators → Visualization + Parameters ↔ Main)

---

## 🛠️ DEVELOPMENT STANDARDS

### Workflow Process
1. **Sequential Thinking**: Analyze requirements, dependencies, edge cases
2. **WBS Planning**: Use `.tasks/WBS.md` for complex tasks → Present for approval → Implement after confirmation
3. **Implementation**: Follow existing patterns, add error handling/type hints/docstrings  
4. **Testing**: Comprehensive validation, regression tests, performance benchmarks
5. **Validation**: Verify against Pine Script reference, measure performance

### Code Quality Requirements
- **Type Safety**: Type hints for all functions and classes
- **Documentation**: Comprehensive docstrings with usage examples
- **Error Handling**: Graceful failure with informative messages
- **Performance**: Vectorized operations, sub-second calculations
- **Testing**: Unit tests for calculations, integration tests for data flow

### Performance Standards
- **Calculation Speed**: Sub-second for live data processing
- **Memory Efficiency**: Stable during extended operation  
- **Mathematical Accuracy**: Exact Pine Script replication
- **Real-time Processing**: Handle continuous websocket data without backlog

---

## 🎯 FOCUSED EXECUTION RULES

### Precision Targeting
- **STICK TO TASK**: No unrelated improvements or scope expansion
- **Direct Solutions**: Root cause focus, avoid over-engineering
- **Minimal Scope**: Modify only specified functions/classes

### File Management
- **Edit First**: ALWAYS prefer editing existing files over creating new ones
- **Purpose-Driven**: Only create files when absolutely necessary
- **No Proactive Docs**: Never create documentation files unless explicitly requested

---

### Documentation & Source  
- **`docs/`**: VuManChu_Cipher_B_Python_Conversion_BRD.md, vmc-market-cipher-b.txt and WBS.md
- **`src/`**: Implementation modules (to be created per 5-module architecture)

### Validation Framework
- **Mathematical Verification**: Compare outputs with Pine Script calculations
- **Performance Benchmarking**: Measure calculation times and memory usage  
- **Visual Accuracy**: Verify chart rendering matches TradingView
- **Data Integrity**: Validate pipeline from Binance to visualization

---

## 🚀 SUCCESS CRITERIA
- **Functional Accuracy**: 100% Pine Script functionality replication
- **Performance Target**: Real-time processing with sub-second calculations  
- **Code Quality**: Production-ready with comprehensive documentation
- **Integration**: Seamless data flow from Binance to visualization
- **Maintainability**: Clear architecture supporting future enhancements