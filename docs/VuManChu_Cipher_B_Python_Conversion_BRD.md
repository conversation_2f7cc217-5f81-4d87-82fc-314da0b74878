# Business Requirements Document (BRD)
## VuManChu Cipher B + Divergences Pine Script to Python Conversion

**Document Version:** 1.0  
**Date:** July 18, 2025  
**Project Type:** New Development (DEV)  
**Author:** <PERSON> (Tech Lead & Solutions Architect)  
**Stakeholder:** <PERSON><PERSON><PERSON><PERSON> (AI Engineer & Solutions Architect)

---

## 1. Executive Summary

### Project Overview
This project involves the systematic conversion of the VuManChu Cipher B + Divergences indicator from Pine Script (TradingView) to Python, creating a foundation for a larger trading system. The conversion will be performance-optimized using computational libraries like ta-lib, and numpy.

### Business Case
- **Objective:** Create a Python-based trading system foundation with real-time capabilities
- **Market Opportunity:** VuManChu Cipher B is a highly popular and profitable trading indicator
- **Technical Goal:** Achieve systematic, complete conversion maintaining all original functionality
- **Strategic Value:** Foundation for automated trading system development

### Key Deliverables
1. **parameters.py** - All Pine Script variables and constants
2. **indicators.py** - All calculation functions using performance-optimized libraries
3. **visualization.py** - Real-time charting using TradingView Lightweight Charts
4. **data_manager.py** - Binance API integration for live and historical data
5. **main.py** - Main execution script

---

## 2. Project Background

### Current State
- **Existing Asset:** VuManChu Cipher B + Divergences Pine Script (provided in input folder)
- **Platform:** TradingView Pine Script v4
- **Limitations:** Locked to TradingView platform, no programmatic access
- **Components:** Multi-indicator system with 7+ technical analysis algorithms

### Desired State
- **Target Platform:** Python-based trading system
- **Capabilities:** Real-time data processing, live visualization, API integration
- **Performance:** Optimized for computational efficiency using ta-lib/numpy
- **Flexibility:** Support for multiple data sources (websocket, CSV, historical API)

### Strategic Alignment
This conversion aligns with building a comprehensive trading system infrastructure that can:
- Process real-time market data
- Execute complex technical analysis
- Provide visual feedback for trading decisions
- Scale to multiple trading strategies

---

## 3. Business Objectives

### Primary Objectives
1. **Complete Systematic Conversion**: Convert 100% of Pine Script functionality to Python
2. **Performance Optimization**: Achieve maximum computational efficiency
3. **Real-time Capability**: Enable live data processing and visualization
4. **Foundation Building**: Create robust base for larger trading system

### Success Metrics
- **Functional Accuracy**: Python output matches Pine Script calculations
- **Performance Benchmark**: Sub-second indicator calculations for live data
- **Visual Fidelity**: Charts match TradingView appearance and behavior
- **Data Integrity**: Successful real-time data processing without errors

---

## 4. Stakeholder Analysis

### Primary Stakeholder
**Apoorv Mintri** - AI Engineer, Tech Lead, Solutions Architect
- **Role:** Project owner and primary developer
- **Responsibilities:** Implementation, testing, validation
- **Technical Expertise:** C#, .NET, Python, cloud services, backend development
- **Decision Authority:** Full project control and technical decisions

### Technical Requirements Alignment
- **Backend Focus:** Emphasis on data processing and system architecture
- **Performance Oriented:** Values computational efficiency and optimization
- **Innovation Focused:** Interested in cutting-edge technical solutions
- **Cloud Native:** Assumes cloud deployment and scalable architecture

---

## 5. Functional Requirements

### 5.1 Core Indicator Components (All Must-Have)

#### 5.1.1 WaveTrend Oscillator
- **REQ-001:** Implement WaveTrend calculation with configurable parameters
  - Channel Length (wtChannelLen): Default 9
  - Average Length (wtAverageLen): Default 12
  - MA Source (wtMASource): Default HLC3
  - MA Length (wtMALen): Default 3
- **REQ-002:** Generate WaveTrend crossover signals (wt1, wt2)
- **REQ-003:** Identify overbought/oversold conditions with configurable levels
- **REQ-004:** Support multi-timeframe analysis capabilities

#### 5.1.2 RSI + MFI Area
- **REQ-005:** Calculate RSI with configurable parameters
  - RSI Source: Default close price
  - RSI Length: Default 14
  - Overbought Level: Default 60
  - Oversold Level: Default 30
- **REQ-006:** Implement MFI (Money Flow Index) calculation
  - MFI Period: Default 60
  - MFI Multiplier: Default 150
  - MFI Y Position: Default 2.5
- **REQ-007:** Generate combined RSI+MFI area visualization

#### 5.1.3 Divergence Detection System
- **REQ-008:** Implement fractal-based divergence detection
  - Regular bullish/bearish divergences
  - Hidden divergences (optional)
  - Configurable OB/OS limits for divergence validation
- **REQ-009:** Support multiple divergence levels
  - Primary divergence levels
  - Secondary divergence levels (additional range)
- **REQ-010:** Generate divergence signals with appropriate colors/markers

#### 5.1.4 Stochastic RSI
- **REQ-011:** Calculate Stochastic RSI with full configurability
  - Stochastic Length: Default 14
  - RSI Length: Default 14
  - K Smooth: Default 3
  - D Smooth: Default 3
  - Log option: Default true
- **REQ-012:** Support averaging of K & D values
- **REQ-013:** Generate stochastic-based divergence signals

#### 5.1.5 Schaff Trend Cycle
- **REQ-014:** Implement Schaff Trend Cycle calculation
  - STC Source: Default close price
  - STC Length: Default 10
  - Fast Length: Default 23
  - Slow Length: Default 50
  - Factor: Default 0.5
- **REQ-015:** Provide optional STC line visualization

#### 5.1.6 Sommi Pattern Recognition
- **REQ-016:** Implement Sommi Flag detection
  - Combine RSI+MFI, WaveTrend, and VWAP conditions
  - Support higher timeframe analysis
  - Generate bearish/bullish flag signals
- **REQ-017:** Implement Sommi Diamond detection
  - Integrate Heiken Ashi candle analysis
  - Support multiple timeframe validation
  - Generate diamond pattern signals

#### 5.1.7 Signal Generation System
- **REQ-018:** Generate buy/sell signals based on WaveTrend crossovers
  - Green circles: Bullish signals at oversold levels
  - Red circles: Bearish signals at overbought levels
  - Small circles: All WaveTrend crossovers
- **REQ-019:** Generate special signals
  - Gold circles: Strong bullish divergence conditions
  - Purple triangles: Divergence-based signals
- **REQ-020:** Support signal filtering and validation

### 5.2 Data Processing Requirements

#### 5.2.1 Data Input Flexibility
- **REQ-021:** Support multiple data sources
  - Real-time via python-binance websocket
  - Historical data via Binance API
  - CSV file import capability
- **REQ-022:** Implement configurable timeframe selection
  - Support all standard timeframes (1m, 5m, 15m, 1h, 4h, 1d)
  - Dynamic timeframe switching capability
- **REQ-023:** Automatic historical data sufficiency
  - Calculate required lookback periods based on parameters
  - Fetch adequate historical data for indicator initialization

#### 5.2.2 Real-time Data Processing
- **REQ-024:** Implement websocket data streaming
  - Real-time price updates via python-binance
  - Immediate indicator recalculation on new data
  - Live chart updates without delay
- **REQ-025:** Support configurable symbol selection
  - Default symbol: BTCUSDT
  - Symbol defined as parameter/variable
- **REQ-026:** Handle streaming data efficiently
  - Rolling window calculations
  - Memory-efficient data management

### 5.3 Calculation Engine Requirements

#### 5.3.1 Performance Optimization
- **REQ-027:** Use performance-optimized libraries
  - ta-lib for standard technical indicators
  - numpy for all array operations and custom calculations
- **REQ-028:** Implement custom algorithms efficiently
  - WaveTrend Oscillator: Pure numpy implementation
  - Divergence Detection: Vectorized numpy algorithms
  - Sommi Patterns: Optimized custom logic
- **REQ-029:** Minimize computational overhead
  - Efficient memory management
  - Vectorized operations over loops
  - Pre-compiled functions for hot paths

#### 5.3.2 Mathematical Accuracy
- **REQ-030:** Maintain calculation precision
  - Exact replication of Pine Script mathematical operations
  - Handle floating-point precision consistently
  - Preserve numerical stability in edge cases

---

## 6. Non-Functional Requirements

### 6.1 Performance Requirements
- **REQ-031:** Calculation Speed: Sub-second indicator calculations for live data
- **REQ-032:** Memory Efficiency: Optimize memory usage for continuous operation
- **REQ-033:** Throughput: Handle real-time data streams without backlog
- **REQ-034:** Scalability: Support multiple timeframes simultaneously

### 6.2 Reliability Requirements
- **REQ-035:** Data Integrity: Ensure accurate data processing without loss
- **REQ-036:** Error Handling: Raise exceptions to stop execution on errors
- **REQ-037:** Stability: Maintain consistent operation during extended runs

### 6.3 Usability Requirements
- **REQ-038:** Configuration Simplicity: Easy parameter modification via parameters.py
- **REQ-039:** Visual Clarity: Clear, TradingView-style chart visualization
- **REQ-040:** Real-time Updates: Immediate visual feedback on new data

### 6.4 Compatibility Requirements
- **REQ-041:** Python Version: Compatible with Python 3.8+
- **REQ-042:** Library Dependencies: Standard installation of required packages
- **REQ-043:** Operating System: Cross-platform compatibility (Windows, Linux, macOS)

---

## 7. UI/UX Requirements

### 7.1 Chart Visualization Architecture
- **REQ-044:** Multi-pane chart layout
  - Main chart: Price candlesticks (top pane)
  - Indicator chart: VuManChu oscillator (separate pane below)
- **REQ-045:** Real-time chart updates
  - Immediate updates on new websocket data
  - Smooth animation without flickering
- **REQ-046:** TradingView-style appearance
  - Professional financial charting aesthetics
  - Consistent color scheme and styling

### 7.2 Signal Visualization
- **REQ-047:** Implement all Pine Script visual elements
  - Colored circles for buy/sell signals
  - Triangles for divergence signals
  - Flags and diamonds for Sommi patterns
- **REQ-048:** Color-coded signal system
  - Green: Bullish signals
  - Red: Bearish signals
  - Gold/Orange: Special conditions
  - Purple: Divergence-based signals
- **REQ-049:** Interactive chart elements
  - Hover tooltips for signal information
  - Zoom and pan capabilities
  - Time-based navigation

### 7.3 Indicator Display
- **REQ-050:** WaveTrend oscillator visualization
  - WT1 and WT2 lines with area fills
  - Overbought/oversold level lines
  - VWAP overlay
- **REQ-051:** RSI+MFI area representation
  - Color-coded area chart
  - Clear positive/negative regions
- **REQ-052:** Additional indicator overlays
  - Stochastic RSI with K/D lines
  - Optional Schaff Trend Cycle line
  - Divergence detection markers

---

## 8. Technical Architecture Requirements

### 8.1 System Architecture
- **REQ-053:** Modular design with clear separation of concerns
  - parameters.py: Configuration management
  - indicators.py: Calculation engine
  - visualization.py: Chart rendering
  - data_manager.py: Data acquisition and management
  - main.py: Orchestration and execution

### 8.2 Technology Stack
- **REQ-054:** Core Libraries
  - pandas, numpy: Data handling and array operations
  - ta-lib: Standard technical indicators
  - python-binance: Market data acquisition
  - lightweight-charts: Real-time visualization
  - websocket-client: Real-time data streaming

### 8.3 Data Flow Architecture
- **REQ-055:** Data pipeline design
  - Data Source → Data Manager → Indicators → Visualization
  - Real-time data streaming with buffering
  - Historical data initialization and management

### 8.4 Performance Architecture
- **REQ-056:** Optimization strategies
  - Numpy arrays over pandas DataFrames where possible
  - Vectorized operations for all calculations
  - Efficient memory management for streaming data
  - JIT compilation for custom functions (if needed)

---

## 9. Out-of-Scope Items

### 9.1 Advanced Features (Future Enhancements)
- **OUT-001:** Runtime parameter modification without restart
- **OUT-002:** Parameter validation and range checking
- **OUT-003:** Preset configuration management
- **OUT-004:** Multi-symbol support and switching
- **OUT-005:** Advanced error recovery mechanisms
- **OUT-006:** Backtesting and strategy execution
- **OUT-007:** Alert system and notifications
- **OUT-008:** Database integration for historical storage

### 9.2 Production Features (Post-PoC)
- **OUT-009:** Comprehensive error handling for edge cases
- **OUT-010:** API rate limiting and retry mechanisms
- **OUT-011:** Logging and monitoring systems
- **OUT-012:** Configuration file management
- **OUT-013:** Unit testing and test automation
- **OUT-014:** Documentation and user guides
- **OUT-015:** Deployment and distribution packaging

### 9.3 Advanced Trading Features
- **OUT-016:** Order execution and trade management
- **OUT-017:** Risk management and position sizing
- **OUT-018:** Portfolio management capabilities
- **OUT-019:** Multi-exchange integration
- **OUT-020:** Advanced charting features (drawings, annotations)

---

## 10. Assumptions and Dependencies

### 10.1 Technical Assumptions
- **ASS-001:** Binance API provides reliable, complete candle data
- **ASS-002:** No missing or incomplete data scenarios
- **ASS-003:** Sufficient historical data always available
- **ASS-004:** No API rate limiting issues during development
- **ASS-005:** Stable internet connection for real-time data

### 10.2 Development Environment
- **ASS-006:** Python 3.8+ environment available
- **ASS-007:** Required libraries can be installed without conflicts
- **ASS-008:** Development machine has sufficient computational resources
- **ASS-009:** Access to TradingView for manual validation

### 10.3 Data Dependencies
- **DEP-001:** python-binance library for market data
- **DEP-002:** Binance exchange API availability
- **DEP-003:** TradingView Lightweight Charts library
- **DEP-004:** ta-lib compilation and installation

### 10.4 External Dependencies
- **DEP-006:** Pine Script reference implementation (provided)
- **DEP-007:** TradingView platform for validation comparison
- **DEP-008:** Stable market data feed during testing
- **DEP-009:** Cross-platform library compatibility

---

## 11. Success Criteria

### 11.1 Functional Success Criteria
- **SC-001:** All Pine Script indicators accurately replicated in Python
- **SC-002:** Visual output matches TradingView charts (manual validation)
- **SC-003:** Real-time data processing without errors or delays
- **SC-004:** All signal generation logic correctly implemented
- **SC-005:** Multi-timeframe support functional and tested

### 11.2 Performance Success Criteria
- **SC-006:** Indicator calculations complete within 1 second for live data
- **SC-007:** Memory usage remains stable during extended operation
- **SC-008:** Chart updates display smoothly without lag
- **SC-009:** System handles continuous websocket data without backlog

### 11.3 Technical Success Criteria
- **SC-010:** Clean, modular code structure with proper separation
- **SC-011:** Efficient use of performance-optimized libraries
- **SC-012:** Robust error handling with informative exception messages
- **SC-013:** Configurable parameters working as expected

### 11.4 Validation Success Criteria
- **SC-014:** Side-by-side comparison with TradingView shows matching results
- **SC-015:** All indicator components produce expected outputs
- **SC-016:** Signal generation timing and accuracy validated
- **SC-017:** Visual elements display correctly with proper colors/shapes

---

## 12. Implementation Approach

### 12.1 3-Phase Development Strategy

#### Phase 1: Parameters and Configuration (parameters.py)
- **Milestone:** All Pine Script variables and constants defined
- **Deliverable:** Complete parameter configuration system
- **Validation:** Parameter values match Pine Script defaults
- **Timeline:** Priority completion (foundational requirement)

#### Phase 2: Calculation Engine (indicators.py)
- **Milestone:** All indicator calculations implemented
- **Deliverable:** Performance-optimized calculation functions
- **Validation:** Numerical output matches Pine Script calculations
- **Timeline:** Core development phase (most complex)

#### Phase 3: Visualization System (visualization.py)
- **Milestone:** Real-time charting with all visual elements
- **Deliverable:** TradingView-style chart with live updates
- **Validation:** Visual comparison with TradingView charts
- **Timeline:** Final integration phase

### 12.2 Validation and Testing Strategy
- **Manual Validation:** Direct comparison with TradingView charts
- **Component Testing:** Individual indicator function validation
- **Integration Testing:** End-to-end system testing with live data
- **Performance Testing:** Computational efficiency validation

### 12.3 Risk Mitigation
- **Complex Algorithm Risk:** Start with WaveTrend (most critical component)
- **Performance Risk:** Early optimization using vectorized operations
- **Accuracy Risk:** Incremental validation against Pine Script
- **Integration Risk:** Modular development with clear interfaces

---

## 13. Quality Assurance

### 13.1 Code Quality Standards
- **QA-001:** Clean, readable code with consistent formatting
- **QA-002:** Proper error handling with informative messages
- **QA-003:** Efficient algorithms using performance-optimized libraries
- **QA-004:** Clear documentation and comments where necessary

### 13.2 Performance Standards
- **QA-005:** Vectorized operations preferred over loops
- **QA-006:** Memory-efficient data structures
- **QA-007:** Minimal computational overhead
- **QA-008:** Optimized for real-time processing

### 13.3 Accuracy Standards
- **QA-009:** Mathematical precision matching Pine Script
- **QA-010:** Consistent numerical results across runs
- **QA-011:** Proper handling of edge cases
- **QA-012:** Validated against reference implementation

---

## 14. Delivery and Acceptance

### 14.1 Deliverables
1. **Complete Python codebase** with all required files
2. **requirements.txt** with all dependencies
3. **Working demonstration** with live data integration
4. **Performance validation** documentation
5. **Setup and usage instructions**

### 14.2 Acceptance Criteria
- **AC-001:** All Pine Script functionality successfully converted
- **AC-002:** Real-time data processing operational
- **AC-003:** Chart visualization matches TradingView appearance
- **AC-004:** Performance meets specified requirements
- **AC-005:** Manual validation confirms accuracy

### 14.3 Final Validation
- **Manual Testing:** Side-by-side comparison with TradingView
- **Performance Testing:** Speed and memory usage validation
- **Integration Testing:** Complete system operation with live data
- **User Acceptance:** Stakeholder approval of final implementation

---

## 15. Project Constraints

### 15.1 Scope Constraints
- **CON-001:** PoC focus - advanced features deferred
- **CON-002:** Single symbol support (BTCUSDT default)
- **CON-003:** Manual validation only (no automated testing)
- **CON-004:** No runtime parameter modification

### 15.2 Technical Constraints
- **CON-005:** Must use specified performance libraries
- **CON-006:** Real-time processing requirement
- **CON-007:** TradingView visual compatibility requirement
- **CON-008:** Complete Pine Script feature parity

### 15.3 Resource Constraints
- **CON-009:** Single developer (Apoorv Mintri)
- **CON-010:** Side project timeline (flexible)
- **CON-011:** Self-service development approach
- **CON-012:** Manual validation resources

---

## Document Control

**Version History:**
- v1.0 (July 18, 2025): Initial comprehensive requirements document

**Approval:**
- **Stakeholder Approval:** Pending (Apoorv Mintri)
- **Technical Review:** Completed (Claude - Tech Lead)
- **Requirements Sign-off:** Pending

**Next Steps:**
1. Stakeholder review and approval
2. Technical architecture finalization
3. Development phase initiation
4. Implementation planning

---

**Total Character Count: 19,847 characters**

*This BRD captures comprehensive requirements for the VuManChu Cipher B Pine Script to Python conversion project, providing a complete foundation for successful implementation.*