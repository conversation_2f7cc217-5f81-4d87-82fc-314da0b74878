"""
VuManChu Cipher B Data Manager
Handles real-time and historical data acquisition from Binance API
"""

import numpy as np
import pandas as pd
import asyncio
import websocket
import json
import time
from typing import Optional, Callable, Dict, List, Tuple
from binance.client import Client
from binance.websockets import BinanceSocketManager
from datetime import datetime, timedelta
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DataManager:
    """
    Manages real-time and historical market data from Binance
    """
    
    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None):
        """
        Initialize DataManager with optional Binance API credentials
        
        Args:
            api_key: Binance API key (optional for public data)
            api_secret: Binance API secret (optional for public data)
        """
        self.client = Client(api_key, api_secret) if api_key and api_secret else Client()
        self.bm = None
        self.conn_key = None
        self.data_buffer = {}
        self.callbacks = []
        self.is_streaming = False
        
    def get_historical_data(
        self, 
        symbol: str = 'BTCUSDT',
        interval: str = '4h',
        lookback_days: int = 100
    ) -> pd.DataFrame:
        """
        Fetch historical OHLCV data from Binance
        
        Args:
            symbol: Trading pair symbol
            interval: Timeframe (1m, 5m, 15m, 1h, 4h, 1d, etc.)
            lookback_days: Number of days to look back
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            # Calculate start time
            start_time = datetime.now() - timedelta(days=lookback_days)
            start_str = start_time.strftime('%Y-%m-%d')
            
            logger.info(f"Fetching historical data for {symbol} ({interval}) from {start_str}")
            
            # Get klines from Binance
            klines = self.client.get_historical_klines(
                symbol, 
                interval, 
                start_str
            )
            
            if not klines:
                raise ValueError(f"No data received for {symbol}")
            
            # Convert to DataFrame
            df = pd.DataFrame(klines, columns=[
                'timestamp', 'open', 'high', 'low', 'close', 'volume',
                'close_time', 'quote_asset_volume', 'number_of_trades',
                'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume', 'ignore'
            ])
            
            # Convert data types
            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # Convert timestamp
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)
            
            # Keep only OHLCV columns
            df = df[['open', 'high', 'low', 'close', 'volume']]
            
            logger.info(f"Successfully fetched {len(df)} candles")
            return df
            
        except Exception as e:
            logger.error(f"Error fetching historical data: {e}")
            raise
    
    def add_callback(self, callback: Callable[[Dict], None]):
        """
        Add callback function for real-time data updates
        
        Args:
            callback: Function to call when new data arrives
        """
        self.callbacks.append(callback)
    
    def _handle_socket_message(self, msg):
        """
        Handle incoming websocket messages
        
        Args:
            msg: Raw websocket message
        """
        try:
            data = json.loads(msg)
            
            if 'k' in data:  # Kline data
                kline = data['k']
                
                # Extract OHLCV data
                candle_data = {
                    'symbol': kline['s'],
                    'timestamp': pd.to_datetime(kline['t'], unit='ms'),
                    'open': float(kline['o']),
                    'high': float(kline['h']),
                    'low': float(kline['l']),
                    'close': float(kline['c']),
                    'volume': float(kline['v']),
                    'is_closed': kline['x']  # True if this kline is closed
                }
                
                # Update buffer
                symbol = candle_data['symbol']
                if symbol not in self.data_buffer:
                    self.data_buffer[symbol] = []
                
                # Add to buffer (keep last 1000 candles)
                self.data_buffer[symbol].append(candle_data)
                if len(self.data_buffer[symbol]) > 1000:
                    self.data_buffer[symbol].pop(0)
                
                # Call callbacks
                for callback in self.callbacks:
                    try:
                        callback(candle_data)
                    except Exception as e:
                        logger.error(f"Error in callback: {e}")
                        
        except Exception as e:
            logger.error(f"Error handling socket message: {e}")
    
    def start_stream(self, symbol: str = 'BTCUSDT', interval: str = '4h'):
        """
        Start real-time data streaming
        
        Args:
            symbol: Trading pair symbol
            interval: Timeframe
        """
        try:
            if self.is_streaming:
                logger.warning("Stream already running")
                return
            
            logger.info(f"Starting stream for {symbol} ({interval})")
            
            self.bm = BinanceSocketManager(self.client)
            self.conn_key = self.bm.start_kline_socket(
                symbol.lower(), 
                self._handle_socket_message,
                interval=interval
            )
            self.bm.start()
            self.is_streaming = True
            
            logger.info("Stream started successfully")
            
        except Exception as e:
            logger.error(f"Error starting stream: {e}")
            raise
    
    def stop_stream(self):
        """
        Stop real-time data streaming
        """
        try:
            if not self.is_streaming:
                logger.warning("No stream running")
                return
            
            logger.info("Stopping stream")
            
            if self.bm and self.conn_key:
                self.bm.stop_socket(self.conn_key)
                self.bm.close()
            
            self.is_streaming = False
            logger.info("Stream stopped")
            
        except Exception as e:
            logger.error(f"Error stopping stream: {e}")
    
    def get_buffer_data(self, symbol: str = 'BTCUSDT') -> pd.DataFrame:
        """
        Get buffered real-time data as DataFrame
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            DataFrame with buffered OHLCV data
        """
        if symbol not in self.data_buffer or not self.data_buffer[symbol]:
            return pd.DataFrame()
        
        df = pd.DataFrame(self.data_buffer[symbol])
        df.set_index('timestamp', inplace=True)
        return df[['open', 'high', 'low', 'close', 'volume']]
    
    def get_latest_price(self, symbol: str = 'BTCUSDT') -> Optional[float]:
        """
        Get latest price for symbol
        
        Args:
            symbol: Trading pair symbol
            
        Returns:
            Latest close price or None
        """
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except Exception as e:
            logger.error(f"Error getting latest price: {e}")
            return None
    
    def validate_data(self, df: pd.DataFrame) -> bool:
        """
        Validate OHLCV data quality
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            True if data is valid
        """
        if df.empty:
            return False
        
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        if not all(col in df.columns for col in required_columns):
            return False
        
        # Check for NaN values
        if df[required_columns].isnull().any().any():
            logger.warning("Data contains NaN values")
            return False
        
        # Check for negative prices
        price_columns = ['open', 'high', 'low', 'close']
        if (df[price_columns] <= 0).any().any():
            logger.warning("Data contains non-positive prices")
            return False
        
        # Check OHLC relationships
        invalid_ohlc = (
            (df['high'] < df['low']) |
            (df['high'] < df['open']) |
            (df['high'] < df['close']) |
            (df['low'] > df['open']) |
            (df['low'] > df['close'])
        )
        
        if invalid_ohlc.any():
            logger.warning("Data contains invalid OHLC relationships")
            return False
        
        return True
    
    def __del__(self):
        """Cleanup on destruction"""
        if self.is_streaming:
            self.stop_stream()
