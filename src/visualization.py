"""
VuManChu Cipher B Visualization
Real-time charting using TradingView Lightweight Charts
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
import json
from datetime import datetime
try:
    from .parameters import VuManChuParams, DEFAULT_PARAMS
except ImportError:
    from parameters import VuManChuParams, DEFAULT_PARAMS


class ChartVisualizer:
    """
    Handles chart visualization for VuManChu Cipher B indicators
    """
    
    def __init__(self, params: Optional[VuManChuParams] = None):
        """
        Initialize chart visualizer
        
        Args:
            params: VuManChu parameter set (uses DEFAULT_PARAMS if None)
        """
        self.params = params if params else DEFAULT_PARAMS
        self.chart_config = self._create_chart_config()
        
    def _create_chart_config(self) -> Dict[str, Any]:
        """
        Create TradingView Lightweight Charts configuration
        
        Returns:
            Chart configuration dictionary
        """
        return {
            'layout': {
                'backgroundColor': '#1e1e1e' if self.params.display.dark_mode else '#ffffff',
                'textColor': '#ffffff' if self.params.display.dark_mode else '#000000',
                'fontSize': 12,
                'fontFamily': 'Arial, sans-serif'
            },
            'grid': {
                'vertLines': {
                    'color': '#2a2a2a' if self.params.display.dark_mode else '#e0e0e0',
                    'style': 1,
                    'visible': True
                },
                'horzLines': {
                    'color': '#2a2a2a' if self.params.display.dark_mode else '#e0e0e0',
                    'style': 1,
                    'visible': True
                }
            },
            'crosshair': {
                'mode': 1,  # Normal crosshair
                'vertLine': {
                    'width': 1,
                    'color': '#758696',
                    'style': 3
                },
                'horzLine': {
                    'width': 1,
                    'color': '#758696',
                    'style': 3
                }
            },
            'timeScale': {
                'timeVisible': True,
                'secondsVisible': False,
                'borderColor': '#485c7b'
            },
            'watermark': {
                'visible': True,
                'fontSize': 24,
                'horzAlign': 'center',
                'vertAlign': 'center',
                'color': 'rgba(171, 71, 188, 0.3)',
                'text': 'VuManChu Cipher B'
            }
        }
    
    def format_ohlcv_data(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """
        Format OHLCV data for TradingView Lightweight Charts
        
        Args:
            df: DataFrame with OHLCV data
            
        Returns:
            List of candlestick data points
        """
        if df.empty:
            return []
        
        candlestick_data = []
        for timestamp, row in df.iterrows():
            candlestick_data.append({
                'time': int(timestamp.timestamp()),
                'open': float(row['open']),
                'high': float(row['high']),
                'low': float(row['low']),
                'close': float(row['close'])
            })
        
        return candlestick_data
    
    def format_line_data(self, timestamps: pd.Index, values: np.ndarray, 
                        name: str = 'line') -> List[Dict[str, Any]]:
        """
        Format line data for TradingView Lightweight Charts
        
        Args:
            timestamps: Timestamp index
            values: Value array
            name: Line name
            
        Returns:
            List of line data points
        """
        if len(timestamps) != len(values):
            raise ValueError("Timestamps and values must have same length")
        
        line_data = []
        for timestamp, value in zip(timestamps, values):
            if not np.isnan(value):
                line_data.append({
                    'time': int(timestamp.timestamp()),
                    'value': float(value)
                })
        
        return line_data
    
    def format_signals(self, timestamps: pd.Index, signals: np.ndarray, 
                      signal_type: str = 'buy') -> List[Dict[str, Any]]:
        """
        Format trading signals for chart markers
        
        Args:
            timestamps: Timestamp index
            signals: Boolean signal array
            signal_type: Type of signal ('buy', 'sell', 'gold')
            
        Returns:
            List of signal markers
        """
        markers = []
        signal_configs = {
            'buy': {
                'color': self.params.colors.colorGreen,
                'shape': 'circle',
                'position': 'belowBar',
                'size': 1
            },
            'sell': {
                'color': self.params.colors.colorRed,
                'shape': 'circle',
                'position': 'aboveBar',
                'size': 1
            },
            'gold': {
                'color': self.params.colors.colorYellow,
                'shape': 'circle',
                'position': 'belowBar',
                'size': 2
            },
            'divergence': {
                'color': '#9c27b0',
                'shape': 'triangleUp',
                'position': 'belowBar',
                'size': 1
            }
        }
        
        config = signal_configs.get(signal_type, signal_configs['buy'])
        
        for i, (timestamp, signal) in enumerate(zip(timestamps, signals)):
            if signal:
                markers.append({
                    'time': int(timestamp.timestamp()),
                    'position': config['position'],
                    'color': config['color'],
                    'shape': config['shape'],
                    'size': config['size'],
                    'text': signal_type.upper()
                })
        
        return markers
    
    def create_wavetrend_series(self, timestamps: pd.Index, wt1: np.ndarray, 
                               wt2: np.ndarray, wt_vwap: np.ndarray) -> Dict[str, Any]:
        """
        Create WaveTrend series configuration
        
        Args:
            timestamps: Timestamp index
            wt1: WaveTrend 1 values
            wt2: WaveTrend 2 values
            wt_vwap: WaveTrend VWAP values
            
        Returns:
            WaveTrend series configuration
        """
        return {
            'wt1': {
                'type': 'line',
                'data': self.format_line_data(timestamps, wt1, 'WT1'),
                'options': {
                    'color': self.params.colors.colorWT1blue,
                    'lineWidth': 2,
                    'title': 'WT1'
                }
            },
            'wt2': {
                'type': 'line',
                'data': self.format_line_data(timestamps, wt2, 'WT2'),
                'options': {
                    'color': self.params.colors.colorWT2purple,
                    'lineWidth': 2,
                    'title': 'WT2'
                }
            },
            'wt_vwap': {
                'type': 'line',
                'data': self.format_line_data(timestamps, wt_vwap, 'WT VWAP'),
                'options': {
                    'color': self.params.colors.VWAPColor,
                    'lineWidth': 1,
                    'title': 'Fast WT',
                    'visible': self.params.wavetrend.vwap_show
                }
            }
        }
    
    def create_rsi_mfi_series(self, timestamps: pd.Index,
                             rsi_mfi: np.ndarray) -> Dict[str, Any]:
        """
        Create RSI+MFI area series configuration

        Args:
            timestamps: Timestamp index
            rsi_mfi: RSI+MFI values

        Returns:
            RSI+MFI series configuration
        """
        # Create positive and negative areas
        positive_area = np.where(rsi_mfi > 0, rsi_mfi, 0)
        negative_area = np.where(rsi_mfi < 0, rsi_mfi, 0)
        
        return {
            'rsi_mfi_positive': {
                'type': 'area',
                'data': self.format_line_data(timestamps, positive_area, 'RSI+MFI+'),
                'options': {
                    'topColor': self.params.colors.rsi_mfi_color_above + '80',
                    'bottomColor': self.params.colors.rsi_mfi_color_above + '00',
                    'lineColor': self.params.colors.rsi_mfi_color_above,
                    'lineWidth': 1,
                    'title': 'RSI+MFI Area +'
                }
            },
            'rsi_mfi_negative': {
                'type': 'area',
                'data': self.format_line_data(timestamps, negative_area, 'RSI+MFI-'),
                'options': {
                    'topColor': self.params.colors.rsi_mfi_color_below + '00',
                    'bottomColor': self.params.colors.rsi_mfi_color_below + '80',
                    'lineColor': self.params.colors.rsi_mfi_color_below,
                    'lineWidth': 1,
                    'title': 'RSI+MFI Area -'
                }
            }
        }
    
    def create_overbought_oversold_lines(self) -> List[Dict[str, Any]]:
        """
        Create overbought/oversold level lines
        
        Returns:
            List of price line configurations
        """
        lines = []
        
        # Overbought levels
        for level, color_alpha in [
            (self.params.wavetrend.ob_level, '40'),
            (self.params.wavetrend.ob_level2, '60'),
            (self.params.wavetrend.ob_level3, '80')
        ]:
            lines.append({
                'price': level,
                'color': self.params.colors.colorRed + color_alpha,
                'lineWidth': 1,
                'lineStyle': 2,  # Dashed
                'axisLabelVisible': True,
                'title': f'OB {level}'
            })
        
        # Oversold levels
        for level, color_alpha in [
            (self.params.wavetrend.os_level, '40'),
            (self.params.wavetrend.os_level2, '60'),
            (self.params.wavetrend.os_level3, '80')
        ]:
            lines.append({
                'price': level,
                'color': self.params.colors.colorGreen + color_alpha,
                'lineWidth': 1,
                'lineStyle': 2,  # Dashed
                'axisLabelVisible': True,
                'title': f'OS {level}'
            })
        
        return lines
    
    def create_complete_chart_data(self, df: pd.DataFrame, 
                                  indicators: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """
        Create complete chart data structure
        
        Args:
            df: OHLCV DataFrame
            indicators: Dictionary of calculated indicators
            
        Returns:
            Complete chart data structure
        """
        timestamps = df.index
        
        # Main candlestick data
        candlestick_data = self.format_ohlcv_data(df)
        
        # WaveTrend series
        wavetrend_series = self.create_wavetrend_series(
            timestamps, 
            indicators['wt1'], 
            indicators['wt2'], 
            indicators['wt_vwap']
        )
        
        # RSI+MFI series
        rsi_mfi_series = self.create_rsi_mfi_series(
            timestamps,
            indicators['rsi_mfi']
        )
        
        # Trading signals
        buy_markers = self.format_signals(timestamps, indicators['buy_signals'], 'buy')
        sell_markers = self.format_signals(timestamps, indicators['sell_signals'], 'sell')
        gold_markers = self.format_signals(timestamps, indicators['gold_signals'], 'gold')
        
        # Combine all markers
        all_markers = buy_markers + sell_markers + gold_markers
        
        # Overbought/Oversold lines
        ob_os_lines = self.create_overbought_oversold_lines()
        
        return {
            'config': self.chart_config,
            'candlestick_data': candlestick_data,
            'wavetrend_series': wavetrend_series,
            'rsi_mfi_series': rsi_mfi_series,
            'markers': all_markers,
            'price_lines': ob_os_lines,
            'last_update': datetime.now().isoformat()
        }
